.chunkPage {
  padding: 24px;
  padding-top: 2px;

  display: flex;
  // height: calc(100vh - 112px);
  height: 100vh;
  flex-direction: column;

  .filter {
    margin: 10px 0;
    display: flex;
    height: 32px;
    justify-content: space-between;
  }

  .pagePdfWrapper {
    width: 60%;
  }

  .pageWrapper {
    width: 100%;
  }

  .pageContent {
    flex: 1;
    width: 100%;
    padding-right: 12px;
    overflow-y: auto;

    .spin {
      min-height: 400px;
    }
  }

  .documentPreview {
    // width: 40%;
    height: calc(100vh - 130px);
    overflow: auto;
  }

  .chunkContainer {
    display: flex;
    // height: calc(100vh - 332px);
    height: calc(100vh - 300px);
  }

  .chunkOtherContainer {
    width: 100%;
  }

  .pageFooter {
    padding-top: 10px;
    padding-right: 10px;
    height: 32px;
  }
}

.container {
  height: 100px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  .content {
    display: flex;
    justify-content: space-between;

    .context {
      flex: 1;
      // width: 207px;
      height: 88px;
      overflow: hidden;
    }
  }

  .footer {
    height: 20px;

    .text {
      margin-left: 10px;
    }
  }
}

.card {
  :global {
    .ant-card-body {
      padding: 10px;
      margin: 0;
    }

    margin-bottom: 10px;
  }

  cursor: pointer;
}
